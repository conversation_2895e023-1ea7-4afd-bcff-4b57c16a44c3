import React, { useState, useEffect } from "react";
import {
  Card,
  Switch,
  Typography,
  DatePicker,
  Radio,
  Button,
  message,
  Divider,
  Skeleton,
  Row,
  Col,
} from "antd";
import dayjs from "dayjs";
import { useAppDispatch } from "../../../hooks/reduxHooks";
import {
  enableAIReply<PERSON>pi,
  getAIStatus<PERSON>pi,
  updateAISchedule<PERSON>pi,
} from "../../../services/aiStatus.service";
import { storeAiStatus } from "../../../store/slices/profile.slice";
import { SaveOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

const AISettingsComponent = () => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [aiEnabled, setAiEnabled] = useState(false);
  const [scheduleType, setScheduleType] = useState("always");
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [currentlyEnabled, setCurrentlyEnabled] = useState(false);

  // Fetch AI status on component mount
  useEffect(() => {
    fetchAIStatus();
  }, []);

  // Helper function to safely parse time strings
  const parseTimeString = (timeStr) => {
    if (!timeStr) return null;

    // Log the incoming time format for debugging
    console.log("[DEBUG] Parsing time string:", timeStr);

    // Try parsing with different formats
    let timeObj;

    // Try as HH:mm format (most likely format from API)
    timeObj = dayjs(timeStr, "HH:mm");
    if (timeObj.isValid()) {
      console.log("[DEBUG] Parsed as HH:mm format:", timeObj.format("HH:mm"));
      return timeObj;
    }

    // Try as full ISO datetime
    timeObj = dayjs(timeStr);
    if (timeObj.isValid()) {
      console.log("[DEBUG] Parsed as ISO format:", timeObj.format("HH:mm"));
      return timeObj;
    }

    // If we couldn't parse it, return null
    console.warn("[DEBUG] Could not parse time string:", timeStr);
    return null;
  };

  const fetchAIStatus = () => {
    setLoading(true);
    dispatch(
      getAIStatusApi({
        successCallback: (response) => {
          // Log the full response for debugging
          console.log("[DEBUG] AI Status API Response:", response);

          setAiEnabled(response.enabled);
          setCurrentlyEnabled(response.is_currently_enabled);

          // Explicitly check for 'enable' schedule with valid times
          // This matches your first API response example
          if (
            response.schedule === "enable" &&
            response.start_time &&
            response.end_time
          ) {
            console.log("[DEBUG] Setting custom schedule mode");
            setScheduleType("custom");

            // Safely parse the time strings
            const start = parseTimeString(response.start_time);
            const end = parseTimeString(response.end_time);

            console.log("[DEBUG] Parsed start time:", start);
            console.log("[DEBUG] Parsed end time:", end);

            if (start && end) {
              setStartTime(start);
              setEndTime(end);
              console.log(
                "[DEBUG] Set custom time range:",
                start.format("h:mm A"),
                "to",
                end.format("h:mm A")
              );
            } else {
              console.warn(
                "[DEBUG] Failed to parse time strings, falling back to always mode"
              );
              // Fall back to always mode if time parsing fails
              setScheduleType("always");
              setStartTime(dayjs().hour(0).minute(0).second(0));
              setEndTime(dayjs().hour(23).minute(59).second(59));
            }
          } else {
            // This matches your second API response example (schedule: null)
            console.log("[DEBUG] Setting always available mode");
            setScheduleType("always");

            // For always mode, set times to cover 24 hours
            const defaultStart = dayjs().hour(0).minute(0).second(0); // 00:00
            const defaultEnd = dayjs().hour(23).minute(59).second(59); // 23:59

            setStartTime(defaultStart);
            setEndTime(defaultEnd);
            console.log("[DEBUG] Set default 24h time range");
          }
        },
        failureCallback: (error) => {
          console.error("[ERROR] Failed to fetch AI status:", error);
          message.error("Failed to fetch AI status");
        },
        finalCallback: () => {
          setLoading(false);
        },
      })
    );
  };

  const handleToggleAI = (checked) => {
    setSaving(true);

    dispatch(
      enableAIReplyApi({
        params: {
          enabled: checked,
        },
        successCallback: (response) => {
          // Storing is_currently_enabled in Redux store
          dispatch(storeAiStatus(response?.is_currently_enabled));

          setAiEnabled(checked); // Only update state after success
          setCurrentlyEnabled(response.is_currently_enabled);
          message.success(
            response?.message || "AI status updated successfully"
          );
        },
        failureCallback: (error) => {
          console.error("[ERROR] Failed to update AI status:", error);
          message.error("Failed to update AI status");
        },
        finalCallback: () => {
          setSaving(false);
        },
      })
    );
  };

  const handleScheduleTypeChange = (e) => {
    const newType = e.target.value;
    console.log("[DEBUG] Schedule type changed to:", newType);
    setScheduleType(newType);

    // For "always" mode, set times to cover 24 hours
    if (newType === "always") {
      const defaultStart = dayjs().hour(0).minute(0).second(0); // 12:00 AM
      const defaultEnd = dayjs().hour(23).minute(59).second(59); // 11:59 PM
      setStartTime(defaultStart);
      setEndTime(defaultEnd);
      console.log("[DEBUG] Reset to 24h default times for always mode");
    }
  };

  const handleDateRangeChange = (dates) => {
    console.log("[DEBUG] Date range changed:", dates);

    if (dates && dates.length === 2) {
      setStartTime(dates[0]);
      setEndTime(dates[1]);
      console.log(
        "[DEBUG] New time range:",
        dates[0].format("h:mm A"),
        "to",
        dates[1].format("h:mm A")
      );
    } else {
      setStartTime(null);
      setEndTime(null);
      console.log("[DEBUG] Cleared time range");
    }
  };

  const validateTimeRange = () => {
    if (!startTime || !endTime) {
      message.warning("Please select both start and end times");
      return false;
    }

    // Make sure the time objects are valid
    if (!startTime.isValid() || !endTime.isValid()) {
      message.warning("Invalid time selection");
      return false;
    }

    // Convert to minutes for easy comparison
    const startMinutes = startTime.hour() * 60 + startTime.minute();
    const endMinutes = endTime.hour() * 60 + endTime.minute();

    if (startMinutes >= endMinutes) {
      message.warning("End time must be after start time");
      return false;
    }

    return true;
  };

  const handleSaveSchedule = () => {
    if (scheduleType === "custom" && !validateTimeRange()) {
      return;
    }

    setSaving(true);
    console.log("[DEBUG] Saving schedule, type:", scheduleType);

    // Prepare params based on schedule type
    let params = {};

    if (scheduleType === "always") {
      // For "always" schedule, we use disable (matching the API expectation)
      params = {
        schedule: "delete",
        // // Still send 24-hour format to API
        // start_time: "00:00",
        // end_time: "23:59",
      };
      console.log("[DEBUG] Sending 'disable' for always schedule");
    } else if (scheduleType === "custom") {
      // Make sure we have valid time objects before formatting
      if (!startTime?.isValid() || !endTime?.isValid()) {
        message.error("Invalid time selection");
        setSaving(false);
        return;
      }

      params = {
        schedule: "enable",
        start_time: startTime.format("HH:mm"),
        end_time: endTime.format("HH:mm"),
      };
      console.log("[DEBUG] Sending custom schedule:", params);
    }

    dispatch(
      updateAIScheduleApi({
        params,
        successCallback: (response) => {
          // Storing is_currently_enabled in Redux store
          dispatch(storeAiStatus(response?.is_currently_enabled));

          message.success("AI schedule updated successfully");
          // Refresh data to ensure UI is in sync with backend
          fetchAIStatus();
        },
        failureCallback: (error) => {
          console.error("[ERROR] Failed to update AI schedule:", error);
          message.error("Failed to update AI schedule");
        },
        finalCallback: () => {
          setSaving(false);
        },
      })
    );
  };

  // Helper to safely format time in 12-hour AM/PM format
  const safeFormatTime = (timeObj) => {
    if (!timeObj || !timeObj.isValid()) {
      return "Invalid time";
    }
    return timeObj.format("h:mm A"); // 12-hour format with AM/PM
  };

  const currentScheduleText = () => {
    if (!aiEnabled) return "";

    if (currentlyEnabled) {
      return "AI assistant is currently active and responding to messages";
    } else {
      return "AI assistant is currently inactive";
    }
  };

  const scheduleDetailsText = () => {
    if (!aiEnabled) return "";

    if (scheduleType === "always") {
      return " (Available 24/7 - 12:00 AM to 11:59 PM)";
    } else if (
      scheduleType === "custom" &&
      startTime &&
      endTime &&
      startTime.isValid() &&
      endTime.isValid()
    ) {
      return ` (Scheduled from ${safeFormatTime(startTime)} to ${safeFormatTime(
        endTime
      )})`;
    }
    return "";
  };

  return (
    <Row gutter={[16, 8]}>
      <Col xs={24}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 12,
          }}
        >
          <div>
            <Text strong style={{ fontSize: 15 }}>
              AI Assistant
            </Text>
            <div>
              <Text type="secondary" style={{ fontSize: 13 }}>
                Enable or disable AI responses to customer messages
              </Text>
            </div>
          </div>
          <Switch
            checked={aiEnabled}
            onChange={handleToggleAI}
            loading={saving}
          />
        </div>
      </Col>

      {aiEnabled && (
        <>
          <Col xs={24}>
            <div style={{ marginBottom: 12 }}>
              <Text strong style={{ fontSize: 15 }}>
                Availability Schedule
              </Text>
              <div>
                <Text type="secondary" style={{ fontSize: 13 }}>
                  Set when your AI assistant should be active
                </Text>
              </div>
            </div>
          </Col>

          <Col xs={24}>
            <Radio.Group
              onChange={handleScheduleTypeChange}
              value={scheduleType}
              style={{ marginBottom: 12, width: "100%" }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 8,
                  width: "100%",
                }}
              >
                <div
                  style={{
                    padding: "12px",
                    border: `1px solid ${
                      scheduleType === "always" ? "#1890ff" : "#e8e8e8"
                    }`,
                    borderRadius: "4px",
                    backgroundColor:
                      scheduleType === "always" ? "#f0f7ff" : "white",
                    cursor: "pointer",
                    transition: "all 0.3s",
                    width: "100%",
                  }}
                  onClick={() => setScheduleType("always")}
                >
                  <Radio value="always" style={{ marginRight: 6 }}>
                    <span style={{ fontWeight: 500, fontSize: "14px" }}>
                      Always Available
                    </span>
                  </Radio>
                  <div style={{ marginLeft: 20, marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      AI will respond at all times when enabled
                    </Text>
                  </div>
                </div>

                <div
                  style={{
                    padding: "12px",
                    border: `1px solid ${
                      scheduleType === "custom" ? "#1890ff" : "#e8e8e8"
                    }`,
                    borderRadius: "4px",
                    backgroundColor:
                      scheduleType === "custom" ? "#f0f7ff" : "white",
                    cursor: "pointer",
                    transition: "all 0.3s",
                    width: "100%",
                  }}
                  onClick={() => setScheduleType("custom")}
                >
                  <Radio value="custom" style={{ marginRight: 6 }}>
                    <span style={{ fontWeight: 500, fontSize: "14px" }}>
                      Custom Schedule
                    </span>
                  </Radio>
                  <div style={{ marginLeft: 20, marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      Set specific times when AI should respond
                    </Text>
                  </div>
                </div>
              </div>
            </Radio.Group>
          </Col>

          {scheduleType === "custom" && (
            <Col xs={24}>
              <div
                style={{
                  backgroundColor: "#fafafa",
                  padding: 12,
                  borderRadius: 4,
                  marginBottom: 12,
                  width: "100%",
                }}
              >
                <RangePicker
                  placeholder={["Start Time", "End Time"]}
                  value={
                    startTime &&
                    endTime &&
                    startTime.isValid() &&
                    endTime.isValid()
                      ? [startTime, endTime]
                      : null
                  }
                  onChange={handleDateRangeChange}
                  style={{ width: "100%" }}
                  allowClear={true}
                  format="h:mm A"
                  picker="time"
                  use12Hours
                />
              </div>
            </Col>
          )}

          <Col xs={24}>
            <div style={{
              display: "flex",
              justifyContent: "flex-end",
              marginTop: 12,
              paddingTop: 12,
              borderTop: "1px solid #f0f0f0"
            }}>
              <Button
                type="primary"
                onClick={handleSaveSchedule}
                loading={saving}
                icon={<SaveOutlined />}
                disabled={
                  scheduleType === "custom" &&
                  (!startTime ||
                    !endTime ||
                    !startTime.isValid() ||
                    !endTime.isValid())
                }
                style={{
                  borderRadius: "6px",
                  height: "36px",
                  paddingLeft: "20px",
                  paddingRight: "20px",
                  fontSize: "14px",
                  fontWeight: "500",
                }}
              >
                Save AI Assistant Settings
              </Button>
            </div>
          </Col>
        </>
      )}

      {/* Status Summary */}
      {aiEnabled && (
        <Col xs={24}>
          <div
            style={{
              marginTop: 12,
              padding: 12,
              backgroundColor: "#fafafa",
              borderRadius: 4,
              border: "1px solid #f0f0f0",
              width: "100%",
            }}
          >
            <Text type="secondary" style={{ fontSize: 12 }}>
              {currentScheduleText() + scheduleDetailsText()}
            </Text>
          </div>
        </Col>
      )}
    </Row>
  );
};

export default AISettingsComponent;
